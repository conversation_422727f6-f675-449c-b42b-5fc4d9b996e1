// server.js
const express = require('express');
const http = require('http');
const { Server } = require('socket.io');
const { Client: SSHClient } = require('ssh2');

const app = express();
const server = http.createServer(app);
const io = new Server(server, { cors: { origin: '*' } });

io.on('connection', (socket) => {
  console.log('Client connected:', socket.id);

  // 1) SSH Terminal namespace
  socket.on('ssh_connect', ({ host, port, username, password }) => {
    const conn = new SSHClient();
    conn.on('ready', () => {
      socket.emit('ssh_data', '\r\n*** SSH CONNECTION ESTABLISHED ***\r\n');
      conn.shell((err, stream) => {
        if (err) return socket.emit('ssh_error', err.message);

        socket.on('ssh_input', data => stream.write(data));
        stream.on('data', data => socket.emit('ssh_data', data.toString()));
        stream.on('close', () => {
          conn.end();
          socket.emit('ssh_close');
        });
      });
    });
    conn.on('error', err => socket.emit('ssh_error', err.message));
    conn.connect({ host, port, username, password });
  });

  // 2) Chat / AI-Mentor namespace (simplified without LiteLLM for now)
  socket.on('chat_message', async ({ message, context }) => {
    try {
      // Simple echo response for now - can be enhanced with actual AI later
      const response = `AI Mentor: I received your message "${message}". This is a placeholder response. To implement actual AI responses, you would integrate with LiteLLM or another AI service.`;
      socket.emit('chat_response', response);
    } catch (e) {
      socket.emit('chat_error', 'Error processing chat message');
    }
  });

  socket.on('disconnect', () => {
    console.log('Client disconnected:', socket.id);
  });
});

app.use(express.static('public'));

const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
  console.log(`Server running on http://localhost:${PORT}`);
});
