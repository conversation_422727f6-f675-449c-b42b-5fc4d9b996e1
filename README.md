# BanditGUI - Browser-based OverTheWire Bandit Client

A web-based interface for connecting to OverTheWire Bandit challenges with an integrated SSH terminal and AI mentor chat.

## Features

- **Real SSH Terminal**: Connect directly to Bandit servers using xterm.js
- **AI Mentor Chat**: Get help with challenges through an integrated chat interface
- **Web-based Interface**: No need to install SSH clients or terminal emulators
- **Real-time Communication**: Uses WebSockets for responsive terminal and chat experience

## Tech Stack

- **Backend**: Node.js, Express, Socket.IO, SSH2
- **Frontend**: HTML5, CSS3, JavaScript, xterm.js
- **Communication**: WebSockets via Socket.IO

## Installation

1. Clone or download this project
2. Install dependencies:
   ```bash
   npm install
   ```

## Usage

1. Start the server:
   ```bash
   npm start
   ```

2. Open your browser and navigate to:
   ```
   http://localhost:3000
   ```

3. Fill in the connection details:
   - **Host**: `bandit.labs.overthewire.org` (default)
   - **Port**: `2220` (default)
   - **Username**: `bandit0` (or your current level)
   - **Password**: The password for your current level

4. Click "Connect" to establish an SSH connection

5. Use the terminal on the left to interact with the Bandit server

6. Use the AI Mentor chat on the right to ask for help with challenges

## Project Structure

```
BanditGUI-o4-mini/
├── server.js              # Main server file with SSH and chat handling
├── package.json           # Node.js dependencies and scripts
├── public/
│   ├── index.html         # Main HTML interface
│   └── client.js          # Frontend JavaScript logic
└── README.md              # This file
```

## How It Works

1. **SSH Connection**: The backend uses the `ssh2` library to establish connections to Bandit servers
2. **Terminal Emulation**: `xterm.js` provides a full-featured terminal in the browser
3. **Real-time Communication**: Socket.IO handles bidirectional communication between frontend and backend
4. **AI Chat**: Simple chat interface (currently with placeholder responses - can be enhanced with actual AI)

## Customization

### Adding Real AI Integration

To add actual AI responses, you can integrate with services like:
- OpenAI API
- LiteLLM
- Local language models
- Other AI services

Replace the placeholder chat handler in `server.js` with your preferred AI integration.

### Styling

Modify the CSS in `public/index.html` to customize the appearance.

### Additional Features

You can extend the project with:
- User authentication
- Session persistence
- Multiple concurrent connections
- File upload/download capabilities
- Command history
- Custom themes

## Security Notes

- This is intended for educational use with OverTheWire Bandit
- SSH credentials are handled in memory only
- Consider adding authentication for production use
- Be mindful of exposing SSH connections over the web

## Troubleshooting

### Connection Issues
- Verify Bandit server details are correct
- Check your internet connection
- Ensure the Bandit servers are accessible

### Server Won't Start
- Make sure all dependencies are installed: `npm install`
- Check that port 3000 is available
- Review console output for error messages

## License

ISC License - Feel free to modify and use for educational purposes.
