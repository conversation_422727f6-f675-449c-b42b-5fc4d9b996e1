<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Bandit Web UI</title>
  <style>
    body { 
      display: flex; 
      height: 100vh; 
      margin: 0; 
      font-family: Arial, sans-serif;
    }
    #terminal { 
      flex: 2; 
      background: #000; 
      position: relative;
    }
    #chat { 
      flex: 1; 
      display: flex; 
      flex-direction: column; 
      border-left: 2px solid #ccc;
      background: #f5f5f5;
    }
    #chatHeader {
      background: #333;
      color: white;
      padding: 10px;
      text-align: center;
      font-weight: bold;
    }
    #messages { 
      flex: 1; 
      overflow-y: auto; 
      padding: 8px; 
    }
    .message {
      margin: 5px 0;
      padding: 8px;
      border-radius: 5px;
    }
    .user {
      background: #e3f2fd;
      text-align: right;
    }
    .bot {
      background: #f1f8e9;
    }
    #input { 
      display: flex; 
      padding: 10px;
      background: white;
      border-top: 1px solid #ccc;
    }
    #chatInput { 
      flex: 1; 
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
      resize: vertical;
    }
    #sendBtn {
      margin-left: 10px;
      padding: 8px 16px;
      background: #007bff;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }
    #sendBtn:hover {
      background: #0056b3;
    }
    #connectionForm {
      position: absolute;
      top: 10px;
      left: 10px;
      background: rgba(255, 255, 255, 0.9);
      padding: 15px;
      border-radius: 5px;
      z-index: 1000;
    }
    #connectionForm input {
      margin: 5px;
      padding: 5px;
      border: 1px solid #ccc;
      border-radius: 3px;
    }
    #connectBtn {
      background: #28a745;
      color: white;
      border: none;
      padding: 8px 16px;
      border-radius: 4px;
      cursor: pointer;
    }
    #connectBtn:hover {
      background: #1e7e34;
    }
  </style>
</head>
<body>
  <div id="terminal">
    <div id="connectionForm">
      <h4>Connect to Bandit</h4>
      <input type="text" id="hostInput" placeholder="Host" value="bandit.labs.overthewire.org" />
      <input type="number" id="portInput" placeholder="Port" value="2220" />
      <input type="text" id="usernameInput" placeholder="Username" value="bandit0" />
      <input type="password" id="passwordInput" placeholder="Password" />
      <button id="connectBtn">Connect</button>
    </div>
  </div>
  <div id="chat">
    <div id="chatHeader">AI Mentor</div>
    <div id="messages"></div>
    <div id="input">
      <textarea id="chatInput" rows="2" placeholder="Ask your mentor for help..."></textarea>
      <button id="sendBtn">Send</button>
    </div>
  </div>
  
  <script src="/socket.io/socket.io.js"></script>
  <script src="https://unpkg.com/xterm@4.19.0/lib/xterm.js"></script>
  <script src="https://unpkg.com/xterm-addon-fit@0.5.0/lib/xterm-addon-fit.js"></script>
  <link rel="stylesheet" href="https://unpkg.com/xterm@4.19.0/css/xterm.css" />
  <script src="client.js"></script>
</body>
</html>
