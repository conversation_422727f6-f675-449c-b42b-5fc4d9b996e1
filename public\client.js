const socket = io();

// ---- Terminal Setup ----
const term = new Terminal({ 
  cursorBlink: true,
  theme: {
    background: '#000000',
    foreground: '#ffffff'
  }
});
const fitAddon = new FitAddon();
term.loadAddon(fitAddon);
term.open(document.getElementById('terminal'));
fitAddon.fit();

// Handle window resize
window.addEventListener('resize', () => {
  fitAddon.fit();
});

// Connection form handling
const connectionForm = document.getElementById('connectionForm');
const hostInput = document.getElementById('hostInput');
const portInput = document.getElementById('portInput');
const usernameInput = document.getElementById('usernameInput');
const passwordInput = document.getElementById('passwordInput');
const connectBtn = document.getElementById('connectBtn');

let isConnected = false;

connectBtn.addEventListener('click', () => {
  if (isConnected) return;
  
  const host = hostInput.value.trim();
  const port = parseInt(portInput.value);
  const username = usernameInput.value.trim();
  const password = passwordInput.value;
  
  if (!host || !port || !username || !password) {
    alert('Please fill in all connection fields');
    return;
  }
  
  connectBtn.textContent = 'Connecting...';
  connectBtn.disabled = true;
  
  socket.emit('ssh_connect', { host, port, username, password });
});

// SSH event handlers
socket.on('ssh_data', (data) => {
  term.write(data);
  if (!isConnected) {
    isConnected = true;
    connectionForm.style.display = 'none';
    term.focus();
  }
});

socket.on('ssh_error', (error) => {
  term.write(`\r\n*** SSH ERROR: ${error} ***\r\n`);
  connectBtn.textContent = 'Connect';
  connectBtn.disabled = false;
  isConnected = false;
});

socket.on('ssh_close', () => {
  term.write('\r\n*** SSH CONNECTION CLOSED ***\r\n');
  connectBtn.textContent = 'Connect';
  connectBtn.disabled = false;
  connectionForm.style.display = 'block';
  isConnected = false;
});

// Terminal input handling
term.onData((data) => {
  if (isConnected) {
    socket.emit('ssh_input', data);
  }
});

// ---- Chat Setup ----
const messagesEl = document.getElementById('messages');
const chatInput = document.getElementById('chatInput');
const sendBtn = document.getElementById('sendBtn');

function appendMsg(className, text) {
  const div = document.createElement('div');
  div.className = `message ${className}`;
  div.textContent = text;
  messagesEl.appendChild(div);
  messagesEl.scrollTop = messagesEl.scrollHeight;
}

function sendMessage() {
  const msg = chatInput.value.trim();
  if (!msg) return;
  
  appendMsg('user', msg);
  socket.emit('chat_message', { message: msg });
  chatInput.value = '';
}

sendBtn.addEventListener('click', sendMessage);

chatInput.addEventListener('keypress', (e) => {
  if (e.key === 'Enter' && !e.shiftKey) {
    e.preventDefault();
    sendMessage();
  }
});

socket.on('chat_response', (reply) => {
  appendMsg('bot', reply);
});

socket.on('chat_error', (error) => {
  appendMsg('bot', `Error: ${error}`);
});

// Initial welcome message
appendMsg('bot', 'Welcome to the Bandit AI Mentor! Connect to a Bandit server and ask me for help with the challenges.');

// Focus on terminal when page loads
term.focus();
