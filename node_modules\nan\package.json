{"name": "nan", "version": "2.23.0", "description": "Native Abstractions for Node.js: C++ header for Node 0.8 -> 24 compatibility", "main": "include_dirs.js", "repository": {"type": "git", "url": "git://github.com/nodejs/nan.git"}, "scripts": {"test": "tap --gc --stderr test/js/*-test.js", "test:worker": "node --experimental-worker test/tap-as-worker.js --gc --stderr test/js/*-test.js", "rebuild-tests-2015": "node-gyp rebuild --msvs_version=2015 --directory test", "rebuild-tests": "node-gyp rebuild --directory test", "docs": "doc/.build.sh"}, "contributors": ["<PERSON>g <<EMAIL>> (https://github.com/rvagg)", "<PERSON> <<EMAIL>> (https://github.com/kkoopa/)", "<PERSON> <<EMAIL>> (https://github.com/trevnorris)", "<PERSON> <<EMAIL>> (https://github.com/TooTallNate)", "<PERSON> <<EMAIL>> (https://github.com/brett19)", "<PERSON> <<EMAIL>> (https://github.com/bnoordhuis)", "<PERSON> <<EMAIL>> (https://github.com/agnat)", "<PERSON> <<EMAIL>> (https://github.com/mkrufky)"], "devDependencies": {"bindings": "~1.2.1", "commander": "^2.8.1", "glob": "^5.0.14", "request": "=2.81.0", "node-gyp": "~v10.3.1", "readable-stream": "^2.1.4", "tap": "~0.7.1", "xtend": "~4.0.0"}, "license": "MIT"}